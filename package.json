{"name": "arabic-calligraphy-creator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "verify-seo": "node scripts/verify-seo.js", "pre-deploy": "node scripts/pre-deployment-check.js", "analyze": "ANALYZE=true npm run build", "submit-indexnow": "node scripts/submit-to-indexnow.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "3.6.0", "embla-carousel-react": "8.5.1", "html2canvas": "^1.4.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-intl": "^4.3.1", "next-themes": "^0.4.4", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}