# Arabic Calligraphy Generator | 阿拉伯书法生成器

<div align="center">

[![Arabic Calligraphy Generator](https://pub-7c6b2100167a48b5877d4c2ab2aa4e3a.r2.dev/og-image.png)](https://arabic-calligraphy-generator.com)

**🎨 Create Stunning Arabic Script Art Online | 在线创建精美阿拉伯书法艺术**

[![Live Demo](https://img.shields.io/badge/🌐_Live_Demo-arabic--calligraphy--generator.com-orange?style=for-the-badge)](https://arabic-calligraphy-generator.com)
[![License](https://img.shields.io/badge/License-MIT-blue?style=for-the-badge)](LICENSE)
[![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)

</div>

## 🌟 Live Demo | 在线演示

**🔗 Try it now: [arabic-calligraphy-generator.com](https://arabic-calligraphy-generator.com)**

Experience our free online Arabic calligraphy generator with 13+ authentic fonts, real-time preview, and high-quality downloads. Create beautiful Arabic script art for personal or commercial use.

体验我们的免费在线阿拉伯书法生成器，提供13+种正宗字体、实时预览和高质量下载。为个人或商业用途创建精美的阿拉伯书法艺术。

## 📋 Table of Contents | 目录

- [Features | 功能特点](#-features--功能特点)
- [Live Demo | 在线演示](#-live-demo--在线演示)
- [Quick Start | 快速开始](#-quick-start--快速开始)
- [Technology Stack | 技术栈](#-technology-stack--技术栈)
- [Font Collection | 字体集合](#-font-collection--字体集合)
- [Usage Guide | 使用指南](#-usage-guide--使用指南)
- [SEO Features | SEO特性](#-seo-features--seo特性)
- [Contributing | 贡献代码](#-contributing--贡献代码)
- [License | 许可证](#-license--许可证)
- [Support | 支持](#-support--支持)

## ✨ Features | 功能特点

### 🎯 Core Features | 核心功能
- **🔤 13+ Premium Arabic Fonts** - Traditional Kufic, Naskh, Thuluth, and modern styles
- **🎨 Advanced Customization** - Colors, gradients, sizes, alignment, and spacing
- **📱 Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **💾 High-Quality Export** - PNG and SVG downloads with transparency support
- **⌨️ Virtual Arabic Keyboard** - Easy text input for non-Arabic speakers
- **🔄 Real-time Preview** - Instant visual feedback as you customize

### 🌐 Website Features | 网站特性
- **🚀 Fast Loading** - Optimized with Next.js 15+ and modern web technologies
- **📊 SEO Optimized** - Structured data, meta tags, and content optimization
- **🔗 Social Sharing** - Easy sharing on social media platforms
- **📚 Educational Content** - Blog posts about Arabic calligraphy history and techniques
- **❓ Comprehensive FAQ** - Detailed answers to common questions

## 🚀 Quick Start | 快速开始

### Online Usage | 在线使用
1. **Visit**: [arabic-calligraphy-generator.com](https://arabic-calligraphy-generator.com)
2. **Type**: Enter your Arabic text or select from templates
3. **Customize**: Choose fonts, colors, and styles
4. **Download**: Export as PNG or SVG

### Local Development | 本地开发

```bash
# Clone the repository
git clone https://github.com/your-username/arabic-calligraphy-creator.git
cd arabic-calligraphy-creator

# Install dependencies
npm install

# Run development server
npm run dev

# Open in browser
# Navigate to http://localhost:3000
```

### Environment Setup | 环境配置

```bash
# Create .env.local file
cp .env.example .env.local

# Edit your environment variables
# Add your analytics and other service keys
```

## 🛠 Technology Stack | 技术栈

### Frontend | 前端
- **Framework**: Next.js 15.2.4 (React 18)
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS 3.4+
- **UI Components**: Radix UI, Lucide Icons
- **Canvas**: html2canvas for image generation

### Backend & Infrastructure | 后端和基础设施
- **Deployment**: Vercel / Netlify compatible
- **Analytics**: Plausible Analytics
- **Performance**: Optimized static generation
- **SEO**: Structured data with JSON-LD

## 📚 Font Collection | 字体集合

Our generator includes **13+ premium Arabic fonts**:

### Traditional Styles | 传统风格
- **Amiri** - Classical Naskh style
- **Scheherazade** - Traditional text typeface
- **Lateef** - Elegant Naskh variant
- **Harmattan** - West African manuscript style

### Modern & Decorative | 现代和装饰性
- **Cairo** - Contemporary geometric
- **Tajawal** - Modern sans-serif
- **Mada** - Clean, readable design
- **Reem Kufi** - Display headline font

### Specialized Fonts | 专业字体
- **Rakkas** - Decorative display type
- **Jomhuria** - Bold decorative style
- **Mirza** - Elegant script variant

**🔗 Explore all fonts**: [arabic-calligraphy-generator.com/#font-collection](https://arabic-calligraphy-generator.com/#font-collection)

## 📖 Usage Guide | 使用指南

### Basic Workflow | 基本工作流程

1. **Text Input | 文本输入**
   - Type Arabic text directly
   - Use virtual keyboard if needed
   - Select from template phrases

2. **Font Selection | 字体选择**
   - Browse 13+ available fonts
   - Preview each font style
   - Consider text length and purpose

3. **Customization | 自定义**
   - Adjust font size and color
   - Apply gradients and effects
   - Set text alignment and spacing

4. **Export | 导出**
   - Download as PNG (for social media)
   - Download as SVG (for print/editing)
   - Copy to clipboard for quick use

### Pro Tips | 专业技巧

- **For Social Media**: Use PNG format with transparent background
- **For Print**: Use SVG format for scalable quality
- **Font Pairing**: Combine traditional and modern fonts for contrast
- **Color Theory**: Use complementary colors for better readability

**📚 Learn more**: [arabic-calligraphy-generator.com/tutorials](https://arabic-calligraphy-generator.com/tutorials)

## 🔍 SEO Features | SEO特性

This project demonstrates modern web development SEO practices:

- **✅ Structured Data**: JSON-LD schema markup
- **✅ Meta Optimization**: Dynamic title and description tags
- **✅ Open Graph**: Social media preview optimization
- **✅ Performance**: Core Web Vitals optimization
- **✅ Accessibility**: WCAG 2.1 compliance
- **✅ Sitemap**: Automated XML sitemap generation

## 🤝 Contributing | 贡献代码

We welcome contributions! Here's how you can help:

### Ways to Contribute | 贡献方式

1. **🐛 Bug Reports**: Report issues via GitHub Issues
2. **💡 Feature Requests**: Suggest new features or improvements
3. **🔧 Code Contributions**: Submit pull requests
4. **📖 Documentation**: Improve README and documentation
5. **🌐 Translations**: Help translate the interface

### Development Process | 开发流程

```bash
# Fork the repository
# Create a feature branch
git checkout -b feature/your-feature-name

# Make your changes
# Test thoroughly
npm run test
npm run build

# Submit a pull request
```

### Code Standards | 代码标准
- TypeScript for type safety
- ESLint and Prettier for code formatting
- Conventional commits for clear history
- Comprehensive testing

## 📄 License | 许可证

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

**Commercial Use**: ✅ Allowed  
**Modification**: ✅ Allowed  
**Distribution**: ✅ Allowed  
**Private Use**: ✅ Allowed

## 🆘 Support | 支持

### Get Help | 获取帮助
- **📧 Contact**: [Contact Form](https://arabic-calligraphy-generator.com/contact)
- **❓ FAQ**: [Frequently Asked Questions](https://arabic-calligraphy-generator.com/faq)
- **🐛 Issues**: [GitHub Issues](https://github.com/your-username/arabic-calligraphy-creator/issues)

### Community | 社区
- **🌟 Star this repo** if you find it useful
- **🔗 Share**: [arabic-calligraphy-generator.com](https://arabic-calligraphy-generator.com)
- **📱 Follow** for updates and new features

---

<div align="center">

**🎨 Create Beautiful Arabic Calligraphy Today!**

[![Try Now](https://img.shields.io/badge/🚀_Try_Now-arabic--calligraphy--generator.com-orange?style=for-the-badge)](https://arabic-calligraphy-generator.com)

**Made with ❤️ for the Arabic calligraphy community**

[Website](https://arabic-calligraphy-generator.com) • [Features](https://arabic-calligraphy-generator.com/features) • [Fonts](https://arabic-calligraphy-generator.com/#font-collection) • [Blog](https://arabic-calligraphy-generator.com/blog) • [Contact](https://arabic-calligraphy-generator.com/contact)

</div>
