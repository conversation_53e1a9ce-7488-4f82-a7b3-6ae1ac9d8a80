import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { getTranslations } from 'next-intl/server'
import { useTranslations } from 'next-intl'
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight, Info, BookOpen, Zap, CheckCircle, Download, ArrowRight } from "lucide-react"
import { getFontInfoBySlug } from "@/app/lib/font-data"
import { downloadFont } from "@/app/lib/font-download"
import { FontCard } from "@/components/font-card"
import { BLOG_LINKS } from "@/lib/content-links"

// 生成metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'fontsPage' });
  const baseUrl = 'https://arabic-calligraphy-generator.com';

  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    keywords: t('metadata.keywords'),
    alternates: {
      canonical: locale === 'en' ? `${baseUrl}/fonts` : `${baseUrl}/${locale}/fonts`,
      languages: {
        'en': `${baseUrl}/fonts`,
        'ar': `${baseUrl}/ar/fonts`,
        'ur': `${baseUrl}/ur/fonts`,
        'bn': `${baseUrl}/bn/fonts`,
        'ms': `${baseUrl}/ms/fonts`,
        'id': `${baseUrl}/id/fonts`,
        'x-default': `${baseUrl}/fonts`,
      },
    },
    openGraph: {
      title: t('metadata.openGraphTitle'),
      description: t('metadata.openGraphDescription'),
      locale: locale === 'en' ? 'en_US' : 'ar_SA',
      alternateLocale: locale === 'en' ? 'ar_SA' : 'en_US',
      type: 'website',
      url: locale === 'en' ? `${baseUrl}/fonts` : `${baseUrl}/${locale}/fonts`,
      siteName: "Arabic Calligraphy Generator",
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.openGraphTitle'),
      description: t('metadata.openGraphDescription'),
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

// 定义字体对象的类型
interface Font {
  name: string;
  slug: string;
  description: string;
  story?: string;
}

// 定义字体分类数据结构 - 只包含字体列表，描述从翻译文件获取
interface FontCategoryData {
  fonts: Font[];
}

const FONT_DATA: Record<string, FontCategoryData> = {
  "Traditional": {
    fonts: [
      { name: "Amiri", slug: "amiri", description: "An elegant revival of Naskh, excellent for body text.", story: "Amiri is a revival of the beautiful Naskh typeface used in the Bulaq Press edition of the Quran (1924), initiated by Dr. Khaled Hosny. It aims to preserve the aesthetic of traditional Naskh while being highly functional for modern digital use." },
      { name: "Scheherazade", slug: "scheherazade", description: "Traditional Naskh style with excellent readability, ideal for extended reading." },
      { name: "Noto Naskh Arabic", slug: "noto-naskh-arabic", description: "Part of Google's Noto family, this Naskh offers clarity for UI and text, optimized for screens." },
      { name: "El Messiri", slug: "el-messiri", description: "Modern Naskh-inspired with a distinctive style, suitable for headings and short texts." },
      { name: "Markazi Text", slug: "markazi-text", description: "A classic Naskh typeface optimized for text readability, particularly in print." },
    ]
  },
  "Kufi": {
    fonts: [
      { name: "Reem Kufi", slug: "reem-kufi", description: "A modern Kufi typeface with geometric precision, great for branding and headlines." },
    ]
  },
  "Diwani": {
    fonts: [
      { name: "Aref Ruqaa", slug: "aref-ruqaa", description: "An ornate typeface inspired by Ottoman Diwani and Ruq'ah calligraphy." },
    ]
  },
  "Nastaliq": {
    fonts: [
      { name: "Lateef", slug: "lateef", description: "A flowing font with Nastaliq influences, perfect for poetry and literary texts." },
      { name: "Mirza", slug: "mirza", description: "A contemporary take on the Persian-influenced Nastaliq style, balancing tradition and modernity." },
    ]
  },
  "Modern": {
    fonts: [
      { name: "Cairo", slug: "cairo", description: "A contemporary Arabic & Latin sans-serif, clean and modern, ideal for web and UI." },
      { name: "Harmattan", slug: "harmattan", description: "A simplified modern Arabic typeface with excellent legibility, especially for smaller sizes." },
      { name: "Mada", slug: "mada", description: "A geometric sans-serif with a minimalist aesthetic, suitable for branding and digital content." },
      { name: "Tajawal", slug: "tajawal", description: "A versatile modern font designed for digital interfaces and multi-script applications." },
      { name: "Lemonada", slug: "lemonada", description: "A rounded and friendly sans-serif font suitable for various uses, adding a touch of warmth." },
    ]
  },
  "Display": {
    fonts: [
      { name: "Jomhuria", slug: "jomhuria", description: "A bold display font with strong visual impact, ideal for large headlines." },
      { name: "Rakkas", slug: "rakkas", description: "A decorative display font with distinctive character, adding a unique flair to designs." },
      { name: "Marhey", slug: "marhey", description: "A playful and energetic display font, perfect for informal and creative projects." },
    ]
  },
};

// 创建简短标语的函数
function createTagline(description: string, maxLength: number = 80): string {
  // 尝试在句子边界处截断
  const sentences = description.split(/[.!?。！？]/);
  if (sentences[0] && sentences[0].length <= maxLength) {
    return sentences[0].trim() + '.';
  }
  // 否则，截断
  return description.substring(0, maxLength - 3) + "...";
}

export default function FontsPage({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const t = useTranslations('fontsPage');
  const structuredDataT = useTranslations('fontsPage.structuredData');

  // 面包屑结构化数据
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": structuredDataT('breadcrumbHome'),
        "item": "https://arabic-calligraphy-generator.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": structuredDataT('breadcrumbFonts'),
        "item": "https://arabic-calligraphy-generator.com/fonts"
      }
    ]
  };

  // 字体集合结构化数据
  const collectionSchema = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": structuredDataT('collectionName'),
    "description": structuredDataT('collectionDescription'),
    "url": "https://arabic-calligraphy-generator.com/fonts",
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": Object.values(FONT_DATA).reduce((total, category) => total + category.fonts.length, 0),
      "itemListElement": Object.entries(FONT_DATA).flatMap(([categoryName, categoryData], categoryIndex) =>
        categoryData.fonts.map((font, fontIndex) => ({
          "@type": "ListItem",
          "position": categoryIndex * 10 + fontIndex + 1,
          "item": {
            "@type": "SoftwareApplication",
            "name": font.name,
            "description": font.description,
            "url": `https://arabic-calligraphy-generator.com/fonts/${font.slug}`,
            "applicationCategory": "Font",
            "operatingSystem": "Web Browser"
          }
        }))
      )
    }
  };

  return (
    <>
      {/* 结构化数据 */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      />
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionSchema) }}
      />
      
      <Navbar />
      <main className="min-h-screen bg-gradient-to-b from-amber-50 to-white py-8 md:py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Page Introduction and Quick Tips */}
            <div className="mb-12 text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-amber-800 mb-4">{t('pageTitle')}</h1>
              <p className="text-lg text-amber-700 leading-relaxed max-w-3xl mx-auto">
                {t('pageDescription')}
              </p>
            </div>

            <section className="mb-12 p-6 bg-sky-100/30 border border-sky-200 rounded-lg shadow">
              <h2 className="text-2xl font-semibold text-sky-800 mb-4 flex items-center">
                <Info className="h-6 w-6 mr-3 text-sky-600" />
                {t('howToChoose.title')}
              </h2>
              <div className="space-y-3 text-sky-700">
                <p className="mb-1">{t('howToChoose.description')}</p>
                <ul className="list-disc list-inside space-y-1.5 pl-2">
                  {t.raw('howToChoose.tips').map((tip: string, index: number) => (
                    <li key={index}><strong>{tip.split(':')[0]}:</strong> {tip.split(':').slice(1).join(':')}</li>
                  ))}
                </ul>
              </div>
            </section>

            {/* Font Categories Display */}
            <div className="space-y-16">
              {Object.entries(FONT_DATA).map(([categoryName, categoryData]) => (
                <div key={categoryName} className="space-y-6">
                  {/* Category Header and Description */}
                  <div className="p-4 border-l-4 border-amber-500 bg-amber-50/50 rounded-r-lg">
                    <h2 className="text-3xl font-bold text-amber-800 mb-3">
                      {t(`fontCategories.${categoryName}`)} {t('stylesLabel')}
                    </h2>
                    <p className="text-amber-700 text-base leading-relaxed mb-3">
                      {t(`categories.${categoryName}.description`)}
                    </p>
                    <div className="mb-2">
                      <h4 className="font-semibold text-amber-700 mb-1.5 text-md flex items-center">
                        <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                        {t('commonApplications')}:
                      </h4>
                      <ul className="list-disc list-inside text-amber-600 text-sm space-y-1 pl-5">
                        {t.raw(`categories.${categoryName}.applications`).map((app: string, index: number) => (
                          <li key={index}>{app}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Fonts within this category */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categoryData.fonts.map((font) => {
                      const fontInfo = getFontInfoBySlug(font.slug);
                      return (
                        <FontCard
                          key={font.slug}
                          name={font.name}
                          slug={font.slug}
                          description={createTagline(font.description, 75)}
                          zipFileName={fontInfo?.zipFileName}
                          displayName={fontInfo?.displayName}
                        />
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Section for "Tips for Using Arabic Fonts Effectively" */}
            <section className="mt-16 mb-12 p-6 bg-green-100/40 border border-green-200 rounded-lg shadow">
              <h2 className="text-2xl font-bold text-green-800 mb-4 flex items-center">
                <BookOpen className="h-6 w-6 mr-3 text-green-600" />
                {t('deeperSection.title')}
              </h2>
              <div className="space-y-3 text-green-700">
                <p>
                  {t('deeperSection.description')}
                </p>
                <ul className="list-disc list-inside space-y-2 pl-2">
                  {t.raw('deeperSection.tips').map((tip: string, index: number) => (
                    <li key={index}>{tip}</li>
                  ))}
                </ul>
                <p className="mt-4">
                  {t('deeperSection.experimentText')} <Link href="/" className="text-green-600 hover:text-green-800 underline font-semibold">{t('deeperSection.generatorLink')}</Link> {t('deeperSection.previewText')}
                </p>
              </div>
            </section>

            {/* Related Blog Articles Section */}
            <section className="mt-16 mb-12">
              <h2 className="text-2xl font-bold text-amber-800 mb-6 text-center">{t('blogSection.title')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  BLOG_LINKS.historyOfCalligraphy,
                  BLOG_LINKS.sixMajorStyles,
                  BLOG_LINKS.modernTypography,
                  BLOG_LINKS.beginnersGuide
                ].map((blog) => (
                  <Card key={blog.href} className="border-amber-200 bg-white/80 backdrop-blur-sm hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <Link href={blog.href} className="group">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-amber-800 group-hover:text-amber-900 mb-2">
                              {blog.title}
                            </h3>
                            <p className="text-amber-600 text-sm mb-3">
                              {blog.description}
                            </p>
                            <div className="flex items-center text-amber-600 group-hover:text-amber-800 text-sm font-medium">
                              <span>{t('blogSection.readArticle')}</span>
                              <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                            </div>
                          </div>
                        </div>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <div className="mt-6 text-center">
                <Button asChild variant="outline" className="border-amber-600 text-amber-600 hover:bg-amber-50">
                  <Link href="/blog">{t('blogSection.viewAllArticles')}</Link>
                </Button>
              </div>
            </section>

            {/* Final CTA */}
            <div className="mt-16 text-center">
              <p className="mb-6 text-lg text-amber-700">
                {t('finalCTA.description')}
              </p>
              <Button size="lg" asChild className="bg-amber-600 hover:bg-amber-700 text-lg px-8 py-6">
                <Link href="/">{t('finalCTA.buttonText')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
