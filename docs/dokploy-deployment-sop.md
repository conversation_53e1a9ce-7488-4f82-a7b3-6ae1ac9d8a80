# Dokploy 新服务部署标准操作程序 (SOP)

## 📋 概述

本文档基于 Plausible Analytics 部署经验，为在现有 Dokploy 环境中部署新服务提供标准化流程。

## 🎯 环境约束条件

### 基础架构
- **服务器 IP**: `***************`
- **主域名**: `myklink.xyz`
- **443 端口**: 被 V2Ray 服务占用（不可用）
- **DNS 提供商**: Cloudflare（直接解析，非代理模式）

### 端口分配策略
```
443:  V2Ray 服务（占用）
8080: Traefik Dashboard
8443: Plausible Analytics
8444: [下一个服务]
8445: [预留]
8446: [预留]
...
```

### 域名规划
- **主域名**: `myklink.xyz`
- **子域名模式**: `service-name.myklink.xyz`

## 🔧 标准部署流程

### 步骤 1: 服务规划

```bash
# 定义服务基本信息
SERVICE_NAME="your-service"                    # 服务名称
SUBDOMAIN="${SERVICE_NAME}.myklink.xyz"        # 子域名
PORT="8444"                                   # 分配端口（递增）
```

### 步骤 2: DNS 配置

在 Cloudflare 控制台中添加 A 记录：
- **类型**: A
- **名称**: `your-service`
- **内容**: `***************`
- **代理状态**: 仅 DNS（灰色云朵，关闭代理）

### 步骤 3: Traefik 入口点配置

```bash
# 1. 备份当前配置
cp /etc/dokploy/traefik/traefik.yml /root/traefik-backup-$(date +%Y%m%d-%H%M).yml

# 2. 编辑 Traefik 配置
nano /etc/dokploy/traefik/traefik.yml
```

在 `entryPoints` 部分添加新端口：
```yaml
entryPoints:
  # 现有配置...
  websecure-8444:
    address ":8444"
```

### 步骤 4: Nginx ACME 转发配置

```bash
# 创建服务专用的 nginx 配置
cat > /etc/nginx/conf.d/${SERVICE_NAME}.conf << EOF
server {
    listen 80;
    server_name ${SUBDOMAIN};

    # 转发 ACME 验证请求到 Traefik
    location /.well-known/acme-challenge/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 其他请求重定向到 HTTPS
    location / {
        return 301 https://\$server_name:${PORT}\$request_uri;
    }
}
EOF

# 测试并重载 nginx 配置
nginx -t && nginx -s reload
```

### 步骤 5: Dokploy 服务部署

1. **创建新应用**
2. **配置域名设置**:
   - Host: `your-service.myklink.xyz`
   - HTTPS: 开启
   - Certificate Provider: `Let's Encrypt`
3. **配置环境变量**（如需要）:
   - `BASE_URL=https://your-service.myklink.xyz:8444/`

### 步骤 6: 修改 Docker Compose 配置

```bash
# 找到服务的 docker-compose.yml 文件
find /etc/dokploy -name "docker-compose.yml" -path "*${SERVICE_NAME}*"

# 编辑文件，修改 Traefik 标签
# 将: traefik.http.routers.xxx.entrypoints=websecure
# 改为: traefik.http.routers.xxx.entrypoints=websecure-8444
```

### 步骤 7: 重启服务

```bash
# 重启 Traefik 容器
docker restart $(docker ps -q --filter "name=traefik")

# 重启新部署的服务
cd /path/to/service/docker-compose/directory
docker-compose restart
```

### 步骤 8: 验证部署

```bash
# 等待 Let's Encrypt 证书申请完成（2-3 分钟）
sleep 180

# 验证 SSL 证书状态
openssl s_client -connect ${SUBDOMAIN}:${PORT} -servername ${SUBDOMAIN} < /dev/null 2>/dev/null | openssl x509 -noout -subject -issuer

# 测试服务可访问性
curl -I https://${SUBDOMAIN}:${PORT}/
```

## 🚨 故障排查指南

### 证书申请失败

1. **检查 nginx ACME 转发**:
   ```bash
   curl -v http://${SUBDOMAIN}/.well-known/acme-challenge/test
   ```
   预期: 请求应该转发到 Traefik，而不是返回 nginx 404

2. **检查 Traefik 日志**:
   ```bash
   docker logs $(docker ps -q --filter "name=traefik") --tail 50 | grep -i "acme\|certificate\|challenge"
   ```

3. **验证端口映射**:
   ```bash
   docker port $(docker ps -q --filter "name=traefik")
   ```

4. **强制重新申请证书**:
   ```bash
   rm -f /etc/dokploy/traefik/dynamic/acme.json
   docker restart $(docker ps -q --filter "name=traefik")
   ```

### 服务无法访问

1. **检查容器状态**:
   ```bash
   docker ps | grep ${SERVICE_NAME}
   ```

2. **检查网络连接**:
   ```bash
   docker network inspect dokploy-network | grep -A 10 ${SERVICE_NAME}
   ```

3. **查看服务日志**:
   ```bash
   docker logs container-name --tail 50
   ```

## 📝 配置模板

### Nginx 配置模板
```nginx
server {
    listen 80;
    server_name SERVICE_DOMAIN;

    location /.well-known/acme-challenge/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        return 301 https://$server_name:SERVICE_PORT$request_uri;
    }
}
```

### Traefik 入口点模板
```yaml
entryPoints:
  websecure-SERVICE_PORT:
    address ":SERVICE_PORT"
```

## 🔄 维护建议

1. **端口分配记录**: 维护一个端口分配表，避免冲突
2. **配置备份**: 每次修改前备份关键配置文件
3. **监控证书**: 定期检查证书有效期
4. **日志监控**: 定期查看 Traefik 和服务日志

## 🎯 实际案例: Plausible Analytics

### 部署配置
- **服务名**: `plausible`
- **域名**: `plausible.myklink.xyz`
- **端口**: `8443`
- **问题**: SSL 证书申请失败（显示 TRAEFIK DEFAULT CERT）
- **根因**: nginx 拦截了 HTTP-01 验证请求
- **解决**: 添加 nginx ACME 转发配置

### 关键配置文件

**nginx 配置** (`/etc/nginx/conf.d/plausible.conf`):
```nginx
server {
    listen 80;
    server_name plausible.myklink.xyz;

    location /.well-known/acme-challenge/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        return 301 https://$server_name:8443$request_uri;
    }
}
```

**Traefik 入口点** (`/etc/dokploy/traefik/traefik.yml`):
```yaml
entryPoints:
  websecure-8443:
    address ":8443"
```

**Docker Compose 标签**:
```yaml
labels:
  - "traefik.http.routers.plausible-plausible-0zckpz-1-websecure.entrypoints=websecure-8443"
```

## 🔍 常见问题 FAQ

### Q: 为什么不能使用 443 端口？
A: 443 端口被 V2Ray 服务占用，用于 VPN 代理功能，不能更改。

### Q: 为什么需要 nginx 转发 ACME 验证？
A: Let's Encrypt 的 HTTP-01 验证需要通过 80 端口访问，但我们的服务运行在自定义端口上，需要 nginx 转发验证请求到 Traefik。

### Q: 可以使用 DNS-01 验证吗？
A: 可以，但需要配置 Cloudflare API 密钥，HTTP-01 验证更简单且已验证可行。

### Q: 如何检查证书是否正确申请？
A: 使用 `openssl s_client -connect domain:port` 命令检查证书颁发者是否为 Let's Encrypt。

### Q: 服务重启后证书丢失怎么办？
A: 检查 `/etc/dokploy/traefik/dynamic/acme.json` 文件是否存在，如果丢失需要重新申请证书。

## 📚 相关文档

- [Traefik 官方文档](https://doc.traefik.io/traefik/)
- [Let's Encrypt 文档](https://letsencrypt.org/docs/)
- [Dokploy 官方文档](https://dokploy.com/docs)
- [nginx 反向代理配置](https://nginx.org/en/docs/http/ngx_http_proxy_module.html)

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 检查 DNS 解析是否正确
2. 验证 nginx 配置语法
3. 查看 Traefik 日志
4. 检查容器网络连接
5. 验证端口映射配置

---

**最后更新**: 2025-07-06
**版本**: 1.0
**基于**: Plausible Analytics 部署经验
**维护者**: 系统管理员
