#!/usr/bin/env node

/**
 * 分析工具诊断脚本
 * 测试 Plausible 和 Google Analytics 的可访问性
 */

const https = require('https');
const http = require('http');

console.log('🔍 开始诊断分析工具问题...\n');

// 测试 Plausible 脚本加载
function testPlausibleScript() {
  return new Promise((resolve) => {
    console.log('📊 测试 Plausible 脚本加载...');
    
    const options = {
      hostname: 'plausible.myklink.xyz',
      port: 8443,
      path: '/js/script.file-downloads.hash.outbound-links.pageview-props.revenue.tagged-events.js',
      method: 'GET',
      rejectUnauthorized: false // 忽略证书错误进行测试
    };

    const req = https.request(options, (res) => {
      console.log(`✅ Plausible 脚本响应状态: ${res.statusCode}`);
      console.log(`📄 Content-Type: ${res.headers['content-type']}`);
      console.log(`🔒 证书状态: ${res.socket.authorized ? '有效' : '无效/自签名'}`);
      
      if (!res.socket.authorized) {
        console.log(`❌ 证书错误: ${res.socket.authorizationError}`);
      }
      
      resolve({
        status: res.statusCode,
        authorized: res.socket.authorized,
        error: res.socket.authorizationError
      });
    });

    req.on('error', (e) => {
      console.log(`❌ Plausible 连接错误: ${e.message}`);
      resolve({ error: e.message });
    });

    req.end();
  });
}

// 测试 Google Analytics 脚本加载
function testGoogleAnalytics() {
  return new Promise((resolve) => {
    console.log('\n📊 测试 Google Analytics 脚本加载...');
    
    const options = {
      hostname: 'www.googletagmanager.com',
      port: 443,
      path: '/gtag/js?id=G-8M8QKWNMRY',
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      console.log(`✅ Google Analytics 响应状态: ${res.statusCode}`);
      console.log(`📄 Content-Type: ${res.headers['content-type']}`);
      console.log(`🔒 证书状态: ${res.socket.authorized ? '有效' : '无效'}`);
      
      resolve({
        status: res.statusCode,
        authorized: res.socket.authorized
      });
    });

    req.on('error', (e) => {
      console.log(`❌ Google Analytics 连接错误: ${e.message}`);
      resolve({ error: e.message });
    });

    req.end();
  });
}

// 测试 Plausible API 健康检查
function testPlausibleHealth() {
  return new Promise((resolve) => {
    console.log('\n🏥 测试 Plausible 健康检查...');
    
    const options = {
      hostname: 'plausible.myklink.xyz',
      port: 8443,
      path: '/api/health',
      method: 'GET',
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      console.log(`✅ Plausible 健康检查状态: ${res.statusCode}`);
      resolve({ status: res.statusCode });
    });

    req.on('error', (e) => {
      console.log(`❌ Plausible 健康检查错误: ${e.message}`);
      resolve({ error: e.message });
    });

    req.end();
  });
}

// 主函数
async function main() {
  const plausibleResult = await testPlausibleScript();
  const googleResult = await testGoogleAnalytics();
  const healthResult = await testPlausibleHealth();

  console.log('\n📋 诊断总结:');
  console.log('=====================================');
  
  if (plausibleResult.error) {
    console.log('❌ Plausible: 连接失败');
  } else if (!plausibleResult.authorized) {
    console.log('⚠️  Plausible: 证书无效 - 这是数据丢失的主要原因！');
    console.log('   浏览器会阻止加载使用无效证书的脚本');
  } else {
    console.log('✅ Plausible: 正常');
  }

  if (googleResult.error) {
    console.log('❌ Google Analytics: 连接失败');
  } else {
    console.log('✅ Google Analytics: 正常');
  }

  console.log('\n🔧 建议解决方案:');
  if (plausibleResult.authorized === false) {
    console.log('1. 为 plausible.myklink.xyz 配置有效的 SSL 证书');
    console.log('2. 使用 Let\'s Encrypt 免费证书');
    console.log('3. 确保 Traefik 正确配置证书解析器');
    console.log('4. 重启 Plausible 服务以应用新证书');
  }
}

main().catch(console.error);
