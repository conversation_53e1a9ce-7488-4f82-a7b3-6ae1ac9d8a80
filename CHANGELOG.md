# Changelog | 更新日志

All notable changes to the Arabic Calligraphy Generator project will be documented in this file.
阿拉伯书法生成器项目的所有重要更改都将记录在此文件中。

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased] | [未发布]

### Added | 新增
- Open source release preparation
- Comprehensive README with project documentation
- Contributing guidelines for community participation
- Issue and PR templates for better collaboration
- MIT license for open source distribution

### Changed | 更改
- Enhanced font collection from 10 to 13 featured fonts
- Improved default display to show 5 fonts with expand option
- Optimized SEO keyword density and content quality

### Fixed | 修复
- Font configuration errors for Markazi Text and Marhey fonts
- Plausible Analytics script loading issues
- Runtime variable reference errors
- Next.js Script component compatibility issues

## [2.0.0] - 2024-01-XX

### Added | 新增
- **🎨 13+ Premium Arabic Fonts**: Traditional and modern styles
- **📱 Responsive Design**: Optimized for all devices
- **⚡ Performance Optimization**: Fast loading with Next.js 15
- **📊 SEO Enhancement**: Structured data and meta optimization
- **🔍 Font Explorer**: Interactive font browsing with details
- **💾 High-Quality Export**: PNG and SVG downloads
- **⌨️ Virtual Arabic Keyboard**: Easy text input
- **🌐 Multi-language Support**: English and Arabic interface

### Changed | 更改
- Migrated from multiple font pages to single-page architecture
- Consolidated all font information into expandable panels
- Improved user experience with better navigation flow
- Enhanced mobile responsiveness and touch interactions

### Fixed | 修复
- Export quality issues with high-resolution outputs
- Font loading performance improvements
- Cross-browser compatibility enhancements
- Mobile layout optimization

## [1.5.0] - 2023-12-XX

### Added | 新增
- Blog section with Arabic calligraphy educational content
- FAQ section with comprehensive user guidance
- Contact and support pages
- Social media sharing functionality

### Improved | 改进
- Text rendering quality and consistency
- Color picker and gradient options
- Template system for quick starts

## [1.0.0] - 2023-11-XX

### Added | 新增
- Initial release of Arabic Calligraphy Generator
- Basic font selection and text customization
- PNG export functionality
- Simple color and size controls
- Desktop-first design

---

## 🔗 Live Website | 在线网站
Visit [arabic-calligraphy-generator.com](https://arabic-calligraphy-generator.com) to try the latest version.
访问[arabic-calligraphy-generator.com](https://arabic-calligraphy-generator.com)体验最新版本。

## 📋 Categories | 分类说明

- **Added | 新增**: New features and functionality
- **Changed | 更改**: Changes in existing functionality
- **Fixed | 修复**: Bug fixes and error corrections
- **Improved | 改进**: Performance and usability enhancements
- **Removed | 移除**: Deprecated or removed features
- **Security | 安全**: Security-related changes 