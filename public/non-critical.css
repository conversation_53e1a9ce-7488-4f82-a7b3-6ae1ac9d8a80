/* 非关键CSS：包含非首屏内容的样式 */

/* 博客页面样式 */
.blog-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.blog-header {
  margin-bottom: 2rem;
  text-align: center;
}

.blog-title {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: rgb(146, 64, 14);
  margin-bottom: 0.5rem;
}

.blog-meta {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(180, 83, 9);
}

.blog-content {
  max-width: none;
}

/* 字体详情页样式 */
.font-detail-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.font-detail-header {
  margin-bottom: 2rem;
}

.font-detail-title {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: rgb(146, 64, 14);
  margin-bottom: 0.5rem;
}

.font-detail-meta {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(180, 83, 9);
}

.font-detail-content {
  max-width: none;
}

/* FAQ页面样式 */
.faq-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.faq-header {
  margin-bottom: 2rem;
  text-align: center;
}

.faq-title {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: rgb(146, 64, 14);
  margin-bottom: 0.5rem;
}

.faq-content {
  max-width: none;
}

/* 联系我们页面样式 */
.contact-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.contact-header {
  margin-bottom: 2rem;
  text-align: center;
}

.contact-title {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: rgb(146, 64, 14);
  margin-bottom: 0.5rem;
}

.contact-content {
  max-width: none;
}

/* 关于我们页面样式 */
.about-container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.about-header {
  margin-bottom: 2rem;
  text-align: center;
}

.about-title {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  color: rgb(146, 64, 14);
  margin-bottom: 0.5rem;
}

.about-content {
  max-width: none;
}

/* 高级组件样式 */
.advanced-features {
  margin-top: 2rem;
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}
